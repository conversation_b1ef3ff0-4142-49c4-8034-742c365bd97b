# Roles and Permissions System Implementation Plan with SpiceDB

## Overview
This plan outlines the implementation of a comprehensive roles and permissions system using SpiceDB (Zanzibar-inspired authorization) to replicate Discord-like permission management functionality.

## Current Architecture Analysis

### Existing Components
- **Backend**: Rust/Axum server with SQLite/PostgreSQL support
- **Authentication**: Firebase Auth with session-based tokens
- **Database Models**: Users, Agents, Tasks, Sessions, basic Permissions
- **Frontend**: Vue 3 + Element Plus with existing user/agent management
- **API**: RESTful endpoints with session authentication middleware

### Current Permission System Limitations
- Simple permission model (entity_type, entity_id, resource_type, resource_id, permission_level)
- No role hierarchy or inheritance
- No fine-grained resource permissions
- No relationship-based permissions

## 1. SpiceDB Integration Architecture

### 1.1 SpiceDB Setup
- **Deployment**: Docker container alongside Ki server
- **Schema**: Define Zanzibar-style relations for roles, users, agents, and resources
- **Connection**: gRPC client from Rust backend to SpiceDB
- **Data Migration**: Migrate existing permissions to SpiceDB tuples

### 1.2 Core Schema Design
```
definition user {}

definition agent {}

definition role {
    relation member: user | agent
    relation parent: role

    permission view = member + parent->view
    permission edit = member + parent->edit
    permission admin = member + parent->admin
}

definition space {
    relation owner: user
    relation admin: user | role#member
    relation member: user | role#member

    permission view = member + admin + owner
    permission edit = admin + owner
    permission manage = owner
}

definition project {
    relation space: space
    relation owner: user
    relation assignee: user | agent

    permission view = space->view + assignee
    permission edit = space->edit + owner
    permission assign = space->admin + owner
}

definition task {
    relation project: project
    relation assignee: user | agent
    relation creator: user

    permission view = project->view + assignee + creator
    permission edit = project->edit + assignee + creator
    permission delete = project->edit + creator
}
```

## 2. Backend Implementation

### 2.1 Dependencies to Add
```toml
# Cargo.toml additions
[dependencies]
authzed = "0.8"
tonic = "0.10"
prost = "0.12"
tokio-stream = "0.1"
```

### 2.2 New Rust Models

#### SpiceDB Client Service
```rust
// src/services/spicedb_service.rs
pub struct SpiceDBService {
    client: PermissionsServiceClient<Channel>,
}

impl SpiceDBService {
    pub async fn new(endpoint: String) -> Result<Self>;
    pub async fn check_permission(&self, subject: &str, resource: &str, permission: &str) -> Result<bool>;
    pub async fn write_relationships(&self, updates: Vec<RelationshipUpdate>) -> Result<()>;
    pub async fn delete_relationships(&self, filter: RelationshipFilter) -> Result<()>;
    pub async fn lookup_subjects(&self, resource: &str, permission: &str) -> Result<Vec<String>>;
    pub async fn lookup_resources(&self, subject: &str, permission: &str, resource_type: &str) -> Result<Vec<String>>;
}
```

#### Role Management Models
```rust
// src/db/models/role.rs
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Role {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub color: String,
    pub icon: Option<String>,
    pub position: i32,
    pub parent_role_id: Option<Uuid>,
    pub space_id: String,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RolePermissions {
    pub role_id: Uuid,
    pub permissions: HashMap<String, Vec<String>>, // resource_type -> [permissions]
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleMember {
    pub role_id: Uuid,
    pub entity_type: EntityType, // User or Agent
    pub entity_id: Uuid,
    pub assigned_by: Uuid,
    pub assigned_at: DateTime<Utc>,
}
```

### 2.3 New API Endpoints

#### Role Management Endpoints
```rust
// src/api/handlers/role_handlers.rs
pub async fn create_role(State(state): State<ServerState>, Json(new_role): Json<NewRole>) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)>;
pub async fn get_roles(State(state): State<ServerState>) -> Result<(StatusCode, Json<Vec<RoleResponse>>), (StatusCode, String)>;
pub async fn update_role(State(state): State<ServerState>, Path(id): Path<Uuid>, Json(update_role): Json<UpdateRole>) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)>;
pub async fn delete_role(State(state): State<ServerState>, Path(id): Path<Uuid>) -> Result<StatusCode, (StatusCode, String)>;

// Role member management
pub async fn add_role_member(State(state): State<ServerState>, Path(role_id): Path<Uuid>, Json(member): Json<NewRoleMember>) -> Result<StatusCode, (StatusCode, String)>;
pub async fn remove_role_member(State(state): State<ServerState>, Path(role_id): Path<Uuid>, Path(entity_id): Path<Uuid>) -> Result<StatusCode, (StatusCode, String)>;
pub async fn get_role_members(State(state): State<ServerState>, Path(role_id): Path<Uuid>) -> Result<(StatusCode, Json<Vec<RoleMemberResponse>>), (StatusCode, String)>;

// Permission management
pub async fn set_role_permissions(State(state): State<ServerState>, Path(role_id): Path<Uuid>, Json(permissions): Json<RolePermissions>) -> Result<StatusCode, (StatusCode, String)>;
pub async fn get_role_permissions(State(state): State<ServerState>, Path(role_id): Path<Uuid>) -> Result<(StatusCode, Json<RolePermissions>), (StatusCode, String)>;

// Permission checking
pub async fn check_permission(State(state): State<ServerState>, Json(check): Json<PermissionCheck>) -> Result<(StatusCode, Json<PermissionCheckResponse>), (StatusCode, String)>;
```

### 2.4 Permission Middleware Enhancement
```rust
// src/api/auth.rs - Enhanced permission checking
pub async fn permission_auth(
    required_permission: &str,
    resource_type: &str,
    resource_id: Option<&str>,
) -> impl Fn(State<ServerState>, Request, Next) -> impl Future<Output = Result<Response, StatusCode>> {
    // Check permission using SpiceDB
    // Extract user from session
    // Call SpiceDB to verify permission
    // Allow/deny request
}
```

## 3. Frontend Implementation

### 3.1 New Pages and Components

#### Roles Management Page (`/roles`)
- **Location**: `enki/src/pages/Roles.vue`
- **Features**:
  - Role list with hierarchy visualization
  - Create/edit role dialog with color picker and icon selector
  - Permission matrix for each role
  - Member management with drag-and-drop
  - Real-time permission preview

#### Permission Management Components
- **RoleCard.vue**: Display role with members count, color, and permissions summary
- **RoleDialog.vue**: Create/edit role with all properties
- **PermissionMatrix.vue**: Grid showing permissions for different resource types
- **RoleMemberManager.vue**: Add/remove members from roles
- **PermissionChecker.vue**: Real-time permission checking tool

#### Enhanced Contacts Page
- **Location**: Update `enki/src/pages/Contacts.vue`
- **Features**:
  - Role badges on user/agent cards
  - Role assignment interface
  - Permission summary per contact
  - Bulk role assignment

### 3.2 New Stores

#### Roles Store
```typescript
// enki/src/stores/rolesStore.ts
export const useRolesStore = defineStore('roles', () => {
  const roles = ref<Role[]>([]);
  const currentRole = ref<Role | null>(null);

  const fetchRoles = async () => { /* API call */ };
  const createRole = async (role: NewRole) => { /* API call + SpiceDB update */ };
  const updateRole = async (id: string, role: UpdateRole) => { /* API call */ };
  const deleteRole = async (id: string) => { /* API call + SpiceDB cleanup */ };

  const addRoleMember = async (roleId: string, entityId: string, entityType: 'user' | 'agent') => { /* API call */ };
  const removeRoleMember = async (roleId: string, entityId: string) => { /* API call */ };

  const setRolePermissions = async (roleId: string, permissions: RolePermissions) => { /* API call */ };
  const checkPermission = async (subject: string, resource: string, permission: string) => { /* API call */ };

  return {
    roles, currentRole,
    fetchRoles, createRole, updateRole, deleteRole,
    addRoleMember, removeRoleMember,
    setRolePermissions, checkPermission
  };
});
```

#### Permissions Store
```typescript
// enki/src/stores/permissionsStore.ts
export const usePermissionsStore = defineStore('permissions', () => {
  const userPermissions = ref<Map<string, Set<string>>>(new Map());
  const permissionCache = ref<Map<string, boolean>>(new Map());

  const checkPermission = async (resource: string, permission: string) => { /* Check with caching */ };
  const hasPermission = (resource: string, permission: string) => { /* Sync check from cache */ };
  const refreshPermissions = async () => { /* Refresh user's permissions */ };

  return { userPermissions, checkPermission, hasPermission, refreshPermissions };
});
```

### 3.3 Router Updates
```typescript
// enki/src/router.ts - Add new routes
{
  path: '/:spaceId/roles',
  name: 'Roles',
  component: () => import('./pages/Roles.vue'),
  meta: { requiresAuth: true, requiredPermission: 'roles.view' }
},
{
  path: '/:spaceId/roles/:roleId',
  name: 'RoleDetail',
  component: () => import('./pages/RoleDetail.vue'),
  meta: { requiresAuth: true, requiredPermission: 'roles.view' }
}
```

## 4. Implementation Phases

### Phase 1: SpiceDB Setup and Basic Integration (Week 1-2)
1. Set up SpiceDB Docker container
2. Implement SpiceDB client service in Rust
3. Create basic schema definitions
4. Implement permission checking middleware
5. Create role management API endpoints

### Phase 2: Role Management Backend (Week 3-4)
1. Implement role CRUD operations
2. Add role member management
3. Implement permission assignment to roles
4. Create role hierarchy support
5. Add data migration from current permission system

### Phase 3: Frontend Role Management (Week 5-6)
1. Create roles management page
2. Implement role creation/editing dialogs
3. Add permission matrix component
4. Create role member management interface
5. Add role hierarchy visualization

### Phase 4: Permission Integration (Week 7-8)
1. Integrate permission checking in existing components
2. Add permission-based UI hiding/showing
3. Implement real-time permission updates
4. Add permission debugging tools
5. Create comprehensive permission testing

### Phase 5: Advanced Features (Week 9-10)
1. Add resource-specific permissions
2. Implement permission inheritance
3. Add bulk permission operations
4. Create permission audit logging
5. Add permission analytics and reporting

## 5. Database Schema Changes

### 5.1 New Tables
```sql
-- Roles table
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) NOT NULL DEFAULT '#5865F2',
    icon VARCHAR(255),
    position INTEGER NOT NULL DEFAULT 0,
    parent_role_id UUID REFERENCES roles(id),
    space_id VARCHAR(255) NOT NULL,
    created_by UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Role members table
CREATE TABLE role_members (
    id UUID PRIMARY KEY,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    entity_type VARCHAR(10) NOT NULL CHECK (entity_type IN ('user', 'agent')),
    entity_id UUID NOT NULL,
    assigned_by UUID NOT NULL,
    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Permission audit log
CREATE TABLE permission_audit_log (
    id UUID PRIMARY KEY,
    action VARCHAR(50) NOT NULL,
    subject_type VARCHAR(10) NOT NULL,
    subject_id UUID NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    permission VARCHAR(50) NOT NULL,
    granted BOOLEAN NOT NULL,
    performed_by UUID NOT NULL,
    performed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## 6. Configuration and Deployment

### 6.1 Docker Compose Updates
```yaml
# Add to docker-compose.yml
services:
  spicedb:
    image: authzed/spicedb:latest
    command: spicedb serve --grpc-preshared-key "your-secret-key" --datastore-engine memory
    ports:
      - "50051:50051"
    environment:
      - SPICEDB_GRPC_PRESHARED_KEY=your-secret-key

  ki-server:
    # ... existing config
    environment:
      - SPICEDB_ENDPOINT=http://spicedb:50051
      - SPICEDB_TOKEN=your-secret-key
    depends_on:
      - spicedb
```

### 6.2 Environment Variables
```bash
# .env additions
SPICEDB_ENDPOINT=http://localhost:50051
SPICEDB_TOKEN=your-secret-key
SPICEDB_SCHEMA_VERSION=1
```

## 7. Testing Strategy

### 7.1 Backend Tests
- Unit tests for SpiceDB service
- Integration tests for role management
- Permission checking tests
- API endpoint tests

### 7.2 Frontend Tests
- Component tests for role management
- Store tests for permission checking
- E2E tests for complete workflows
- Permission UI tests

## 8. Migration Strategy

### 8.1 Data Migration
1. Export existing permissions to SpiceDB format
2. Create default roles based on current permission levels
3. Assign users to appropriate roles
4. Verify permission equivalence
5. Switch to SpiceDB-based checking

### 8.2 Rollback Plan
- Keep existing permission system as fallback
- Feature flag for SpiceDB vs legacy permissions
- Gradual migration with A/B testing
- Performance monitoring and comparison

## 9. UI/UX Design Specifications

### 9.1 Roles Overview Page
**Layout**: Similar to Discord's Server Settings > Roles
- **Header**: "Roles" title with "Create Role" button
- **Default Permissions Section**: @everyone role with basic permissions
- **Roles List**: Scrollable list showing:
  - Role color indicator (circle)
  - Role name and member count
  - Drag handle for reordering
  - Three-dot menu for actions

### 9.2 Role Detail/Edit Dialog
**Tabs**: Display, Permissions, Links, Manage Members
- **Display Tab**:
  - Role name input field
  - Color picker (Discord-style color palette)
  - Icon selector with emoji/image upload
  - Role hierarchy position
- **Permissions Tab**:
  - Permission matrix with categories (General, Text, Voice, etc.)
  - Toggle switches for each permission
  - Permission inheritance indicator
- **Manage Members Tab**:
  - Search bar for adding members
  - Current members list with remove buttons
  - Bulk actions for member management

### 9.3 Permission Matrix Component
**Structure**: Grid layout with resource types as columns, permissions as rows
- **Visual Indicators**:
  - Green checkmark for granted permissions
  - Red X for denied permissions
  - Gray dash for inherited permissions
  - Yellow warning for conflicting permissions
- **Interactive Features**:
  - Click to toggle permissions
  - Hover tooltips explaining each permission
  - Bulk select/deselect options

### 9.4 Enhanced Contacts Page
**User/Agent Cards**: Add role badges below name
- **Role Badges**: Small colored pills showing role names
- **Role Assignment**: Click to open role assignment dialog
- **Permission Summary**: Expandable section showing effective permissions

## 10. API Specifications

### 10.1 Role Management Endpoints
```
GET    /api/roles                    - List all roles
POST   /api/roles                    - Create new role
GET    /api/roles/{id}               - Get role details
PUT    /api/roles/{id}               - Update role
DELETE /api/roles/{id}               - Delete role

GET    /api/roles/{id}/members       - Get role members
POST   /api/roles/{id}/members       - Add member to role
DELETE /api/roles/{id}/members/{uid} - Remove member from role

GET    /api/roles/{id}/permissions   - Get role permissions
PUT    /api/roles/{id}/permissions   - Set role permissions
```

### 10.2 Permission Checking Endpoints
```
POST   /api/permissions/check        - Check specific permission
POST   /api/permissions/bulk-check   - Check multiple permissions
GET    /api/permissions/user/{id}    - Get user's effective permissions
GET    /api/permissions/audit        - Get permission audit log
```

### 10.3 Request/Response Formats
```typescript
// Role creation request
interface CreateRoleRequest {
  name: string;
  description?: string;
  color: string;
  icon?: string;
  parent_role_id?: string;
  permissions?: Record<string, string[]>;
}

// Permission check request
interface PermissionCheckRequest {
  subject_id: string;
  subject_type: 'user' | 'agent';
  resource_type: string;
  resource_id?: string;
  permission: string;
}

// Permission check response
interface PermissionCheckResponse {
  granted: boolean;
  reason?: string;
  inherited_from?: string;
}
```

## 11. Security Considerations

### 11.1 Permission Validation
- Validate all permission checks server-side
- Implement rate limiting for permission checks
- Log all permission changes for audit
- Prevent privilege escalation attacks

### 11.2 Role Hierarchy Security
- Prevent circular role dependencies
- Limit role nesting depth
- Validate role assignments based on user's own permissions
- Implement role assignment approval workflows

### 11.3 SpiceDB Security
- Use secure gRPC connections with TLS
- Implement proper authentication tokens
- Regular backup of SpiceDB data
- Monitor for unusual permission patterns

## Next Steps
1. **Review and Approval**: Stakeholder review of this comprehensive plan
2. **Environment Setup**: Configure development environment with SpiceDB
3. **Phase 1 Kickoff**: Begin SpiceDB integration and basic role management
4. **Technical Specifications**: Create detailed specs for each component
5. **Monitoring Setup**: Implement logging and monitoring for the new system
6. **Testing Framework**: Set up comprehensive testing infrastructure
7. **Documentation**: Create user guides and API documentation