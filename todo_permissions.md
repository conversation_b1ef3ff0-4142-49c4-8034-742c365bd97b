# Roles and Permissions System Implementation Plan with SpiceDB

## Overview
This plan outlines the implementation of a comprehensive roles and permissions system using SpiceDB (Zanzibar-inspired authorization) to replicate Discord-like permission management functionality.

**CRITICAL PRINCIPLE**: SpiceDB is the **single source of truth** for all authorization relationships. The application database stores only metadata (names, colors, descriptions) - never relationships or permissions.

## Current Architecture Analysis

### Existing Components
- **Backend**: Rust/Axum server with SQLite/PostgreSQL support
- **Authentication**: Firebase Auth with session-based tokens
- **Database Models**: Users, Agents, Tasks, Sessions, basic Permissions
- **Frontend**: Vue 3 + Element Plus with existing user/agent management
- **API**: RESTful endpoints with session authentication middleware

### Current Permission System Limitations
- Simple permission model (entity_type, entity_id, resource_type, resource_id, permission_level)
- No role hierarchy or inheritance
- No fine-grained resource permissions
- No relationship-based permissions

## 1. SpiceDB Integration Architecture

### 1.1 SpiceDB Setup
- **Deployment**: Docker container alongside Ki server
- **Schema**: Define Zanzibar-style relations for roles, users, agents, and resources
- **Connection**: gRPC client from Rust backend to SpiceDB
- **Data Migration**: Migrate existing permissions to SpiceDB tuples
- **Single Source of Truth**: All relationships live in SpiceDB only

### 1.2 Corrected Core Schema Design
```
definition user {}

definition agent {}

// A role is just a grouper of subjects and a node in a hierarchy.
// NO permissions are defined here - they're defined on protected resources.
definition role {
    relation member: user | agent
    relation parent: role
}

definition space {
    relation owner: user
    relation admin: user | role#member
    relation member: user | role#member

    // Permissions are unions of relations
    permission view = member + admin + owner
    permission edit_space_settings = admin + owner
    permission manage_roles = admin + owner
    permission manage_space = owner
}

definition project {
    relation space: space          // Link back to the containing space
    relation owner: user
    relation assignee: user | agent
    relation viewer: user | role#member
    relation editor: user | role#member

    // Permissions inherit from the parent space AND can be granted directly
    permission view = assignee + owner + viewer + space->view
    permission edit = owner + editor + space->edit_space_settings
    permission assign = owner + space->manage_roles
}

definition task {
    relation project: project
    relation assignee: user | agent
    relation creator: user
    relation viewer: user | role#member
    relation editor: user | role#member

    // Permissions inherit from project and can be granted directly
    permission view = assignee + creator + viewer + project->view
    permission edit = assignee + creator + editor + project->edit
    permission delete = creator + project->edit
}
```

## 2. Backend Implementation

### 2.1 Dependencies to Add
```toml
# Cargo.toml additions
[dependencies]
authzed = "0.8"
tonic = "0.10"
prost = "0.12"
tokio-stream = "0.1"
```

### 2.2 New Rust Models

#### SpiceDB Client Service
```rust
// src/services/spicedb_service.rs
pub struct SpiceDBService {
    client: PermissionsServiceClient<Channel>,
}

impl SpiceDBService {
    pub async fn new(endpoint: String) -> Result<Self>;
    pub async fn check_permission(&self, subject: &str, resource: &str, permission: &str) -> Result<bool>;
    pub async fn write_relationships(&self, updates: Vec<RelationshipUpdate>) -> Result<()>;
    pub async fn delete_relationships(&self, filter: RelationshipFilter) -> Result<()>;
    pub async fn lookup_subjects(&self, resource: &str, permission: &str) -> Result<Vec<String>>;
    pub async fn lookup_resources(&self, subject: &str, permission: &str, resource_type: &str) -> Result<Vec<String>>;
}
```

#### Role Management Models (Corrected - Metadata Only)
```rust
// src/db/models/role.rs
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Role {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub color: String,
    pub icon: Option<String>,
    pub position: i32,           // For UI ordering only
    pub space_id: String,        // For scoping roles to a space
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    // REMOVED: parent_role_id - hierarchy lives in SpiceDB
    // REMOVED: Any member or permission data - lives in SpiceDB
}

// For API requests to grant permissions on resources
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrantPermissionRequest {
    pub resource_type: String,   // "project", "task", "space"
    pub resource_id: Uuid,
    pub relation: String,        // "viewer", "editor", "admin"
    pub subject_type: String,    // "user", "agent", "role#member"
    pub subject_id: Uuid,
}

// For API requests to add members to roles
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddRoleMemberRequest {
    pub entity_type: EntityType, // User or Agent
    pub entity_id: Uuid,
}

// Response for role members (fetched from SpiceDB)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleMemberResponse {
    pub entity_type: EntityType,
    pub entity_id: Uuid,
    pub entity_name: String,     // Fetched from users/agents table
    pub entity_avatar: Option<String>,
}
```

### 2.3 Corrected API Endpoints (SpiceDB-First)

#### Role Metadata Management (SQL Database)
```rust
// src/api/handlers/role_handlers.rs
// These only manage role metadata, NOT relationships
pub async fn create_role(State(state): State<ServerState>, Json(new_role): Json<NewRole>) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)>;
pub async fn get_roles(State(state): State<ServerState>) -> Result<(StatusCode, Json<Vec<RoleResponse>>), (StatusCode, String)>;
pub async fn update_role(State(state): State<ServerState>, Path(id): Path<Uuid>, Json(update_role): Json<UpdateRole>) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)>;
pub async fn delete_role(State(state): State<ServerState>, Path(id): Path<Uuid>) -> Result<StatusCode, (StatusCode, String)>;
```

#### Role Relationship Management (SpiceDB Only)
```rust
// Role member management - ONLY talks to SpiceDB
pub async fn add_role_member(State(state): State<ServerState>, Path(role_id): Path<Uuid>, Json(member): Json<AddRoleMemberRequest>) -> Result<StatusCode, (StatusCode, String)> {
    // Writes tuple: role:abc#member@user:123 (or @agent:123)
    // NO SQL database writes
}

pub async fn remove_role_member(State(state): State<ServerState>, Path(role_id): Path<Uuid>, Path(entity_id): Path<Uuid>) -> Result<StatusCode, (StatusCode, String)> {
    // Deletes tuple: role:abc#member@user:123
    // NO SQL database writes
}

pub async fn get_role_members(State(state): State<ServerState>, Path(role_id): Path<Uuid>) -> Result<(StatusCode, Json<Vec<RoleMemberResponse>>), (StatusCode, String)> {
    // Queries SpiceDB for role:abc#member relationships
    // Then fetches user/agent metadata from SQL for display
}

// Role hierarchy management - ONLY talks to SpiceDB
pub async fn set_role_parent(State(state): State<ServerState>, Path(role_id): Path<Uuid>, Json(parent): Json<SetRoleParentRequest>) -> Result<StatusCode, (StatusCode, String)> {
    // Writes tuple: role:child#parent@role:parent
}
```

#### Resource Permission Management (SpiceDB Only)
```rust
// ELIMINATED: set_role_permissions - This was the anti-pattern!
// REPLACED WITH: Resource-centric permission granting

// Grant a role permission on a specific resource
pub async fn grant_resource_permission(State(state): State<ServerState>, Json(grant): Json<GrantPermissionRequest>) -> Result<StatusCode, (StatusCode, String)> {
    // Examples:
    // project:123#viewer@role:abc#member
    // task:456#editor@user:789
    // space:home#admin@role:moderators#member
}

pub async fn revoke_resource_permission(State(state): State<ServerState>, Json(revoke): Json<RevokePermissionRequest>) -> Result<StatusCode, (StatusCode, String)> {
    // Deletes specific permission tuples
}

pub async fn get_resource_permissions(State(state): State<ServerState>, Path(resource_type): Path<String>, Path(resource_id): Path<Uuid>) -> Result<(StatusCode, Json<Vec<PermissionTuple>>), (StatusCode, String)> {
    // Lists all permission tuples for a resource
}
```

#### Permission Checking (SpiceDB Only)
```rust
pub async fn check_permission(State(state): State<ServerState>, Json(check): Json<PermissionCheck>) -> Result<(StatusCode, Json<PermissionCheckResponse>), (StatusCode, String)> {
    // Direct SpiceDB check: can user:123 edit project:456?
}

pub async fn bulk_check_permissions(State(state): State<ServerState>, Json(checks): Json<Vec<PermissionCheck>>) -> Result<(StatusCode, Json<Vec<PermissionCheckResponse>>), (StatusCode, String)> {
    // Batch permission checking for UI
}
```

### 2.4 Permission Middleware Enhancement
```rust
// src/api/auth.rs - Enhanced permission checking
pub async fn permission_auth(
    required_permission: &str,
    resource_type: &str,
    resource_id: Option<&str>,
) -> impl Fn(State<ServerState>, Request, Next) -> impl Future<Output = Result<Response, StatusCode>> {
    // Check permission using SpiceDB
    // Extract user from session
    // Call SpiceDB to verify permission
    // Allow/deny request
}
```

## 3. Frontend Implementation

### 3.1 New Pages and Components

#### Roles Management Page (`/roles`)
- **Location**: `enki/src/pages/Roles.vue`
- **Features**:
  - Role list with hierarchy visualization
  - Create/edit role dialog with color picker and icon selector
  - Permission matrix for each role
  - Member management with drag-and-drop
  - Real-time permission preview

#### Permission Management Components
- **RoleCard.vue**: Display role with members count, color, and permissions summary
- **RoleDialog.vue**: Create/edit role with all properties
- **PermissionMatrix.vue**: Grid showing permissions for different resource types
- **RoleMemberManager.vue**: Add/remove members from roles
- **PermissionChecker.vue**: Real-time permission checking tool

#### Enhanced Contacts Page
- **Location**: Update `enki/src/pages/Contacts.vue`
- **Features**:
  - Role badges on user/agent cards
  - Role assignment interface
  - Permission summary per contact
  - Bulk role assignment

### 3.2 New Stores

#### Roles Store
```typescript
// enki/src/stores/rolesStore.ts
export const useRolesStore = defineStore('roles', () => {
  const roles = ref<Role[]>([]);
  const currentRole = ref<Role | null>(null);

  const fetchRoles = async () => { /* API call */ };
  const createRole = async (role: NewRole) => { /* API call + SpiceDB update */ };
  const updateRole = async (id: string, role: UpdateRole) => { /* API call */ };
  const deleteRole = async (id: string) => { /* API call + SpiceDB cleanup */ };

  const addRoleMember = async (roleId: string, entityId: string, entityType: 'user' | 'agent') => { /* API call */ };
  const removeRoleMember = async (roleId: string, entityId: string) => { /* API call */ };

  const setRolePermissions = async (roleId: string, permissions: RolePermissions) => { /* API call */ };
  const checkPermission = async (subject: string, resource: string, permission: string) => { /* API call */ };

  return {
    roles, currentRole,
    fetchRoles, createRole, updateRole, deleteRole,
    addRoleMember, removeRoleMember,
    setRolePermissions, checkPermission
  };
});
```

#### Permissions Store
```typescript
// enki/src/stores/permissionsStore.ts
export const usePermissionsStore = defineStore('permissions', () => {
  const userPermissions = ref<Map<string, Set<string>>>(new Map());
  const permissionCache = ref<Map<string, boolean>>(new Map());

  const checkPermission = async (resource: string, permission: string) => { /* Check with caching */ };
  const hasPermission = (resource: string, permission: string) => { /* Sync check from cache */ };
  const refreshPermissions = async () => { /* Refresh user's permissions */ };

  return { userPermissions, checkPermission, hasPermission, refreshPermissions };
});
```

### 3.3 Router Updates
```typescript
// enki/src/router.ts - Add new routes
{
  path: '/:spaceId/roles',
  name: 'Roles',
  component: () => import('./pages/Roles.vue'),
  meta: { requiresAuth: true, requiredPermission: 'roles.view' }
},
{
  path: '/:spaceId/roles/:roleId',
  name: 'RoleDetail',
  component: () => import('./pages/RoleDetail.vue'),
  meta: { requiresAuth: true, requiredPermission: 'roles.view' }
}
```

## 4. Implementation Phases

### Phase 1: SpiceDB Setup and Basic Integration (Week 1-2)
1. Set up SpiceDB Docker container
2. Implement SpiceDB client service in Rust
3. Create basic schema definitions
4. Implement permission checking middleware
5. Create role management API endpoints

### Phase 2: Role Management Backend (Week 3-4)
1. Implement role CRUD operations
2. Add role member management
3. Implement permission assignment to roles
4. Create role hierarchy support
5. Add data migration from current permission system

### Phase 3: Frontend Role Management (Week 5-6)
1. Create roles management page
2. Implement role creation/editing dialogs
3. Add permission matrix component
4. Create role member management interface
5. Add role hierarchy visualization

### Phase 4: Permission Integration (Week 7-8)
1. Integrate permission checking in existing components
2. Add permission-based UI hiding/showing
3. Implement real-time permission updates
4. Add permission debugging tools
5. Create comprehensive permission testing

### Phase 5: Advanced Features (Week 9-10)
1. Add resource-specific permissions
2. Implement permission inheritance
3. Add bulk permission operations
4. Create permission audit logging
5. Add permission analytics and reporting

## 5. Corrected Database Schema Changes

### 5.1 New Tables (Metadata Only - No Relationships)
```sql
-- Roles table (CORRECTED - ONLY METADATA)
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) NOT NULL DEFAULT '#5865F2',
    icon VARCHAR(255),
    position INTEGER NOT NULL DEFAULT 0,    -- For UI ordering only
    space_id VARCHAR(255) NOT NULL,          -- For scoping roles to a space
    created_by UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    -- REMOVED: parent_role_id - hierarchy lives in SpiceDB
    -- REMOVED: Any member or permission columns
);

-- ELIMINATED: role_members table - This data lives in SpiceDB as tuples
-- ELIMINATED: role_permissions table - This data lives in SpiceDB as tuples

-- Permission audit log (Optional - for compliance/debugging)
CREATE TABLE permission_audit_log (
    id UUID PRIMARY KEY,
    action VARCHAR(50) NOT NULL,             -- 'check', 'grant', 'revoke'
    subject_type VARCHAR(10) NOT NULL,       -- 'user', 'agent'
    subject_id UUID NOT NULL,
    resource_type VARCHAR(50) NOT NULL,      -- 'space', 'project', 'task'
    resource_id UUID,
    permission VARCHAR(50) NOT NULL,         -- 'view', 'edit', 'admin'
    granted BOOLEAN NOT NULL,                -- Result of permission check
    performed_by UUID NOT NULL,              -- Who performed the action
    performed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    spicedb_token VARCHAR(255)               -- SpiceDB consistency token for debugging
);
```

### 5.2 Migration Strategy from Current System
```sql
-- Step 1: Create new roles table
-- Step 2: Migrate existing permission data to SpiceDB tuples
-- Step 3: Drop old permissions table after verification
-- Step 4: Update application to use SpiceDB-only APIs

-- Example migration script (pseudo-code):
-- For each existing permission record:
--   1. Create role if it doesn't exist (based on permission_level)
--   2. Add user/agent to role: role:admin#member@user:123
--   3. Grant role permission on resource: project:456#editor@role:admin#member
--   4. Verify permission check works
--   5. Mark record as migrated
```

## 6. Configuration and Deployment

### 6.1 Docker Compose Updates
```yaml
# Add to docker-compose.yml
services:
  spicedb:
    image: authzed/spicedb:latest
    command: spicedb serve --grpc-preshared-key "your-secret-key" --datastore-engine memory
    ports:
      - "50051:50051"
    environment:
      - SPICEDB_GRPC_PRESHARED_KEY=your-secret-key

  ki-server:
    # ... existing config
    environment:
      - SPICEDB_ENDPOINT=http://spicedb:50051
      - SPICEDB_TOKEN=your-secret-key
    depends_on:
      - spicedb
```

### 6.2 Environment Variables
```bash
# .env additions
SPICEDB_ENDPOINT=http://localhost:50051
SPICEDB_TOKEN=your-secret-key
SPICEDB_SCHEMA_VERSION=1
```

## 7. Testing Strategy

### 7.1 Backend Tests
- Unit tests for SpiceDB service
- Integration tests for role management
- Permission checking tests
- API endpoint tests

### 7.2 Frontend Tests
- Component tests for role management
- Store tests for permission checking
- E2E tests for complete workflows
- Permission UI tests

## 8. Migration Strategy

### 8.1 Data Migration
1. Export existing permissions to SpiceDB format
2. Create default roles based on current permission levels
3. Assign users to appropriate roles
4. Verify permission equivalence
5. Switch to SpiceDB-based checking

### 8.2 Rollback Plan
- Keep existing permission system as fallback
- Feature flag for SpiceDB vs legacy permissions
- Gradual migration with A/B testing
- Performance monitoring and comparison

## 9. UI/UX Design Specifications

### 9.1 Roles Overview Page
**Layout**: Similar to Discord's Server Settings > Roles
- **Header**: "Roles" title with "Create Role" button
- **Default Permissions Section**: @everyone role with basic permissions
- **Roles List**: Scrollable list showing:
  - Role color indicator (circle)
  - Role name and member count
  - Drag handle for reordering
  - Three-dot menu for actions

### 9.2 Role Detail/Edit Dialog
**Tabs**: Display, Permissions, Links, Manage Members
- **Display Tab**:
  - Role name input field
  - Color picker (Discord-style color palette)
  - Icon selector with emoji/image upload
  - Role hierarchy position
- **Permissions Tab**:
  - Permission matrix with categories (General, Text, Voice, etc.)
  - Toggle switches for each permission
  - Permission inheritance indicator
- **Manage Members Tab**:
  - Search bar for adding members
  - Current members list with remove buttons
  - Bulk actions for member management

### 9.3 Permission Matrix Component
**Structure**: Grid layout with resource types as columns, permissions as rows
- **Visual Indicators**:
  - Green checkmark for granted permissions
  - Red X for denied permissions
  - Gray dash for inherited permissions
  - Yellow warning for conflicting permissions
- **Interactive Features**:
  - Click to toggle permissions
  - Hover tooltips explaining each permission
  - Bulk select/deselect options

### 9.4 Enhanced Contacts Page
**User/Agent Cards**: Add role badges below name
- **Role Badges**: Small colored pills showing role names
- **Role Assignment**: Click to open role assignment dialog
- **Permission Summary**: Expandable section showing effective permissions

## 10. API Specifications

### 10.1 Corrected API Endpoints (SpiceDB-First Architecture)

#### Role Metadata Endpoints (SQL Database)
```
GET    /api/roles                    - List all roles (metadata only)
POST   /api/roles                    - Create new role (metadata only)
GET    /api/roles/{id}               - Get role details (metadata only)
PUT    /api/roles/{id}               - Update role (metadata only)
DELETE /api/roles/{id}               - Delete role (metadata + SpiceDB cleanup)
```

#### Role Relationship Endpoints (SpiceDB Only)
```
GET    /api/roles/{id}/members       - Get role members (from SpiceDB)
POST   /api/roles/{id}/members       - Add member to role (SpiceDB only)
DELETE /api/roles/{id}/members/{uid} - Remove member from role (SpiceDB only)
PUT    /api/roles/{id}/parent        - Set role parent (SpiceDB only)
DELETE /api/roles/{id}/parent        - Remove role parent (SpiceDB only)
```

#### Resource Permission Endpoints (SpiceDB Only)
```
POST   /api/permissions/grant        - Grant permission on resource
POST   /api/permissions/revoke       - Revoke permission on resource
GET    /api/permissions/resource     - Get all permissions for a resource
```

#### Permission Checking Endpoints (SpiceDB Only)
```
POST   /api/permissions/check        - Check specific permission
POST   /api/permissions/bulk-check   - Check multiple permissions
GET    /api/permissions/user/{id}    - Get user's effective permissions
GET    /api/permissions/audit        - Get permission audit log
```

#### ELIMINATED Endpoints (Anti-patterns)
```
❌ PUT    /api/roles/{id}/permissions   - REMOVED: Roles don't "have" permissions
❌ GET    /api/roles/{id}/permissions   - REMOVED: Permissions are on resources
```

### 10.3 Corrected Request/Response Formats
```typescript
// Role creation request (metadata only)
interface CreateRoleRequest {
  name: string;
  description?: string;
  color: string;
  icon?: string;
  space_id: string;
  // REMOVED: parent_role_id - set via separate endpoint
  // REMOVED: permissions - granted via separate endpoints
}

// Grant permission request (SpiceDB tuple creation)
interface GrantPermissionRequest {
  resource_type: string;    // "project", "task", "space"
  resource_id: string;
  relation: string;         // "viewer", "editor", "admin"
  subject_type: string;     // "user", "agent", "role#member"
  subject_id: string;
}

// Add role member request
interface AddRoleMemberRequest {
  entity_type: 'user' | 'agent';
  entity_id: string;
}

// Permission check request
interface PermissionCheckRequest {
  subject_id: string;
  subject_type: 'user' | 'agent';
  resource_type: string;
  resource_id: string;
  permission: string;
}

// Permission check response
interface PermissionCheckResponse {
  granted: boolean;
  debug_trace?: string;     // SpiceDB debug information
  consistency_token?: string;
}

// Role member response (enriched with metadata)
interface RoleMemberResponse {
  entity_type: 'user' | 'agent';
  entity_id: string;
  entity_name: string;      // Fetched from SQL
  entity_avatar?: string;   // Fetched from SQL
  added_at: string;         // From SpiceDB if available
}
```

## 11. Security Considerations

### 11.1 Permission Validation
- Validate all permission checks server-side
- Implement rate limiting for permission checks
- Log all permission changes for audit
- Prevent privilege escalation attacks

### 11.2 Role Hierarchy Security
- Prevent circular role dependencies
- Limit role nesting depth
- Validate role assignments based on user's own permissions
- Implement role assignment approval workflows

### 11.3 SpiceDB Security
- Use secure gRPC connections with TLS
- Implement proper authentication tokens
- Regular backup of SpiceDB data
- Monitor for unusual permission patterns

## Critical Architecture Fixes Applied

### ✅ Fixed: Eliminated Dual System Anti-Pattern
- **Before**: Role members stored in both SQL (`role_members` table) AND SpiceDB
- **After**: Role members stored ONLY in SpiceDB as tuples (`role:abc#member@user:123`)
- **Benefit**: Single source of truth, no synchronization issues, no race conditions

### ✅ Fixed: Corrected Schema Design
- **Before**: Permissions defined on `role` objects (ambiguous)
- **After**: Permissions defined on protected resources (`project`, `task`, `space`)
- **Benefit**: Clear permission model, proper Zanzibar semantics

### ✅ Fixed: Eliminated Anti-Pattern APIs
- **Removed**: `PUT /api/roles/{id}/permissions` (roles don't "have" permissions)
- **Added**: `POST /api/permissions/grant` (grant roles permissions on resources)
- **Benefit**: Resource-centric permission management, proper tuple creation

### ✅ Fixed: Simplified Database Schema
- **Before**: Complex tables for members, permissions, hierarchy
- **After**: Single `roles` table with metadata only
- **Benefit**: Simpler data model, no complex transactions across systems

## Revised Implementation Strategy

### Phase 1: Pure SpiceDB Foundation (Week 1-2)
1. Set up SpiceDB with corrected schema
2. Implement SpiceDB-only client service
3. Create resource-centric permission APIs
4. NO dual-system code - SpiceDB only

### Phase 2: Role Metadata + SpiceDB Relationships (Week 3-4)
1. Create roles table (metadata only)
2. Implement role member management (SpiceDB only)
3. Implement role hierarchy (SpiceDB only)
4. Create permission granting APIs (SpiceDB only)

### Phase 3: Frontend with Proper Architecture (Week 5-6)
1. Build role management UI that understands the separation
2. Role metadata editing (talks to SQL)
3. Role member management (talks to SpiceDB via API)
4. Permission granting UI (resource-centric)

### Phase 4: Migration and Testing (Week 7-8)
1. Migrate existing permissions to SpiceDB tuples
2. Comprehensive testing of permission checks
3. Performance testing and optimization
4. Rollback procedures

## Key Principles for Implementation

1. **SpiceDB is the Single Source of Truth** for all relationships
2. **SQL Database stores only metadata** (names, colors, descriptions)
3. **No dual writes** - each piece of data has exactly one home
4. **Resource-centric permissions** - grant roles access to resources, not vice versa
5. **Tuple-based thinking** - every permission is a relationship tuple

## Next Steps
1. **Architecture Review**: Confirm this corrected approach
2. **SpiceDB Setup**: Deploy with the corrected schema
3. **Proof of Concept**: Build one complete flow (role creation → member addition → permission granting → checking)
4. **Team Training**: Ensure team understands Zanzibar principles
5. **Implementation**: Follow the revised phases strictly